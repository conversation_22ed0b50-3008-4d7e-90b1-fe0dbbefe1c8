# Image Compression Node.js

## Development

### Local Development

```bash
npm install
npm run dev
```

### Docker Development with Hot Reload

```bash
# Using Docker Compose
docker-compose -f docker-compose.dev.yaml up --build

# Using Podman Compose (recommended script)
./dev.sh

# Using Podman Compose (manual with user mapping)
USER_ID=$(id -u) GROUP_ID=$(id -g) podman-compose -f docker-compose.dev.yaml up --build

# For rootless Podman with SELinux (RHEL/Fedora)
./dev-rootless.sh
```

## Production

### Docker Production

```bash
# Using Docker Compose
docker-compose up --build

# Using Podman Compose
podman-compose up --build

# Or use the convenience script
./prod.sh
```

## Access the Application

```
open http://localhost:3000
```

## Features

- Hot reload in development mode
- Multi-stage Docker builds for optimized production images
- Support for both Docker and Podman
- PostgreSQL and Redis integration

## Troubleshooting

### Podman Permission Issues

If you encounter `EACCES: permission denied` errors with <PERSON><PERSON>:

1. **Use the provided scripts**: `./dev.sh` automatically handles user ID mapping
2. **Manual user mapping**: Set USER_ID and GROUP_ID environment variables
3. **SELinux issues**: Use `./dev-rootless.sh` for systems with SELinux enabled
4. **Volume permissions**: Ensure your user has read/write access to the project directory

### Common Podman vs Docker Differences

- Podman runs rootless by default, requiring user ID mapping
- SELinux context may need to be set on RHEL/Fedora systems
- Volume mounts need `:Z` flag for SELinux systems
