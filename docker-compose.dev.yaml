version: "3.8"
services:
  api:
    privileged: true
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:${PORT}"
    volumes:
      - .:/app:Z # :Z for SELinux context (Podman/RHEL systems)
    environment:
      - NODE_ENV=development
      - PORT=${PORT}
      - LOG_LEVEL=debug
      - STATIC_PATH=static
      - ADJUST_IMAGE_DEFAULT_QUALITY=99
      - ADJUST_IMAGE_DEFAULT_MAX_FILES=10
      - ADJUST_IMAGE_MAX_FILE_SIZE=50000000
      - DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/compress_images
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FILE_SRV_TYPE=local
    depends_on:
      - db
      - redis
    # Enable hot reload by watching file changes
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: sync
          path: ./package.json
          target: /app/package.json
        - action: rebuild
          path: ./package.json

  redis:
    image: redis:7.0.11
    ports:
      - "6379:6379"

  db:
    image: postgres:15.3
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=compress_images
    volumes:
      - db_data:/var/lib/postgresql/data

  worker:
    privileged: true
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: pnpm worker
    depends_on:
      - redis
    volumes:
      - .:/app:Z # :Z for SELinux context (Podman/RHEL systems)
    environment:
      - NODE_ENV=development
      - PORT=${PORT}
      - LOG_LEVEL=debug
      - STATIC_PATH=static
      - ADJUST_IMAGE_DEFAULT_QUALITY=99
      - ADJUST_IMAGE_DEFAULT_MAX_FILES=10
      - ADJUST_IMAGE_MAX_FILE_SIZE=50000000
      - DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/compress_images
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - FILE_SRV_TYPE=local
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: sync
          path: ./package.json
          target: /app/package.json
        - action: rebuild
          path: ./package.json

volumes:
  db_data:
