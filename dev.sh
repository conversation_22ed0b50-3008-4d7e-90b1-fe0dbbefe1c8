#!/bin/bash

# Ensure script stops on first error
set -e

# Check if podman-compose is installed
if ! command -v podman-compose &> /dev/null; then
    echo "podman-compose is not installed. Please install it first."
    exit 1
fi

echo "Starting development environment with hot reload..."

# Ensure the static directory exists
mkdir -p static/compress_images

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Warning: .env file not found. Creating a basic one..."
    cat > .env << EOF
JWT_SECRET=supersecret-jwt-key-for-development-at-least-32-characters
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres
PORT=3000
EOF
fi

# Start development environment with hot reload (privileged mode handles permissions)
podman-compose -f docker-compose.dev.yaml up --build

# This script will be interrupted by Ctrl+C, which will trigger podman-compose down
# The following will only execute if podman-compose up exits normally
echo "Stopping development environment..."
podman-compose -f docker-compose.dev.yaml down
