import z from "zod/v4";
import { format } from "date-fns";
import type { FastifyPluginAsync } from "fastify";
import type { ZodTypeProvider } from "fastify-type-provider-zod";
import { appConfig } from "../../config";
import { FileRepository } from "../../repositories/file.repository";
import { LocalFileService } from "../../services/local.fileService";
import { ImageService } from "../../services/image.service";

const imageRoutes: FastifyPluginAsync = async (fastify) => {
	fastify.withTypeProvider<ZodTypeProvider>().route({
		method: "POST",
		url: "/compress",
		schema: {
			querystring: z.object({
				quality: z.coerce
					.number()
					.min(1)
					.max(100)
					.default(appConfig.adjustImage.defaultQuality),

				targetSize: z.coerce
					.number()
					.min(1)
					.max(appConfig.adjustImage.maxFileSize),
			}),
		},
		handler: async (request, reply) => {
			const { quality, targetSize } = request.query;

			const parts = request.files({
				limits: { fileSize: appConfig.adjustImage.maxFileSize },
				throwFileSizeLimit: true,
			});

			const today = new Date();

			const fileRepository = new FileRepository();

			const localFileService = new LocalFileService(
				`compress_images/${format(today, "yyyy-MM")}/compressed`,
				fileRepository,
			);

			const imageService = new ImageService(localFileService);

			for await (const part of parts) {
				const { file, filename, mimetype } = part;

				imageService.uploadAndCompress({
					file,
					quality,
					targetSize,
					fileName: filename,
					mimeType: mimetype,
					onTruncated: () => {
						reply.send(new fastify.multipartErrors.FilesLimitError());
					},
				});
			}
		},
	});
};

export default imageRoutes;
