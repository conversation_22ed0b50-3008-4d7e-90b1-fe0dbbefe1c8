import { type Job, Worker } from "bullmq";
import container from "../container";
import { ImageService } from "../services/image.service";
import logger from "../utils/logger";
import type {
	CompressImageJobData,
	CompressImageJobResult,
} from "../types/image.types";
import { redisConnection } from "../config";
import { JOB_NAMES } from "../constants/job-names";

export async function processCompressImageJob(
	job: Job<CompressImageJobData, CompressImageJobResult>,
) {
	const { id, name, data } = job;

	logger.info(
		{ jobId: id, jobName: name, data },
		"Processing compress image job",
	);

	try {
		const imageService = container.resolve(ImageService);
		await imageService.handleCompressImageJob(data);

		logger.info({ jobId: id }, "Job processed successfully");
	} catch (error) {
		logger.error({ err: error, jobId: id }, "Job processing failed");
		// Re-throw the error to allow BullMQ to handle the job failure (e.g., retry)
		throw error;
	}
}

export const imageWorker = new Worker(
	"image-processing",
	async (job: Job) => {
		switch (job.name) {
			case JOB_NAMES.IMAGE.COMPRESS: {
				console.log(`job: ${JOB_NAMES.IMAGE.COMPRESS}`);
				break;
			}
		}
	},
	{
		connection: redisConnection,
	},
);

imageWorker.on("active", () => {
	console.log("image worker activative");
});
