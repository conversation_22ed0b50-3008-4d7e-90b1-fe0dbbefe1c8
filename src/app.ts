import "reflect-metadata";
import Fastify from "fastify";
import hyperid from "hyperid";
import imageRouter from "./routes/v1/image.route";
import multipartPlugin from "@fastify/multipart";

import {
	serializerCompiler,
	validatorCompiler,
} from "fastify-type-provider-zod";
import { errorHandler } from "./middlewares/error-handler";
import logger from "./utils/logger";

const app = Fastify({
	loggerInstance: logger,
	genReqId: hyperid({ fixedLength: true }),
});

app.setValidatorCompiler(validatorCompiler);
app.setSerializerCompiler(serializerCompiler);

app.register(multipartPlugin, { throwFileSizeLimit: false });
app.register(imageRouter, { prefix: "/api/images" });

app.setErrorHandler(errorHandler);

app.listen({ port: 3000 }, (err) => {
	if (err) {
		app.log.error(err);
		process.exit(1);
	}
});
