import type stream from "node:stream";
import type { Logger } from "pino";
import { inject, singleton } from "tsyringe";
import { addCompressImageJob } from "../queues";
import { BadRequestError, ServiceError } from "../types/errors";
import type { CompressImageJobData } from "../types/image.types";
import {
  compressImageToTargetSize,
  createFriendlyFileName,
  getFileExtension,
} from "../utils/file";
import logger from "../utils/logger";
import type { FileService } from "./file.service";

@singleton()
export class ImageService {
  private logger: Logger;

  constructor(@inject("FileService") private fileService: FileService) {
    this.logger = logger;
  }

  async upload(args: {
    file: stream.Readable & { truncated: boolean };
    fileName: string;
    mimeType: string;
    onTruncated?: () => void;
  }) {
    const { file, fileName, mimeType, onTruncated } = args;

    try {
      let { truncated: isTruncated } = file;

      const { id } = await this.fileService.uploadStream({
        fileName,
        mimeType,
        readStream: file,
        onFinish: () => {
          isTruncated = file.truncated;
        },
        onError: (error) => {
          this.logger.error(error);

          throw error;
        },
      });

      if (isTruncated) {
        onTruncated?.();
      }

      return { id };
    } catch (error) {
      throw new ServiceError("Upload File Fail!", error as Error);
    }
  }

  async uploadAndCompress(args: {
    file: stream.Readable & { truncated: boolean };
    fileName: string;
    mimeType: string;
    quality: number;
    targetSize: number;
    onTruncated?: () => void;
  }): Promise<void> {
    const { file, mimeType, quality, onTruncated, targetSize } = args;

    const fileName = createFriendlyFileName(args.fileName);

    try {
      const { id } = await this.upload({
        file,
        fileName,
        mimeType,
        onTruncated,
      });

      const fileExt = getFileExtension(fileName);

      if (!fileExt) {
        throw new BadRequestError(
          `Can not parse file extension, file name: ${fileName}`
        );
      }

      const imageInfo = await this.fileService.getFileInfo(id);

      addCompressImageJob({
        imageInfo: {
          ...imageInfo,
          id,
        },
        options: {
          quality,
          targetFormat: fileExt,
          targetSize,
        },
      });
    } catch (error) {
      throw new ServiceError("Compress File Fail!", error as Error);
    }
  }

  async handleCompressImageJob(data: CompressImageJobData) {
    const { imageInfo, options } = data;
    const { id, name: originalFileName } = imageInfo;
    const { quality, targetFormat, targetSize } = options;

    this.logger.info(
      { fileId: id, quality, targetFormat, targetSize },
      "Start compressing image"
    );

    try {
      // 1. Create streams for reading the original file and writing the compressed file
      const readStream = this.fileService.createReadStream(originalFileName);
      const compressedFileName = `compressed-${originalFileName}`;
      const writeStream = this.fileService.createWriteStream(
        compressedFileName
      );

      // 2. Get the original file buffer to pass to the compression function
      // Note: This might be inefficient for very large files.
      // A better approach for large files would be to modify compressImageToTargetSize
      // to work directly with streams. For now, we'll use a buffer.
      const chunks = [];
      for await (const chunk of readStream) {
        chunks.push(chunk);
      }
      const originalBuffer = Buffer.concat(chunks);

      // 3. Compress the image
      await compressImageToTargetSize({
        buffer: originalBuffer,
        outputStream: writeStream,
        targetSizeInBytes: targetSize,
        imageOptions: {
          quality,
          targetFormat,
        },
        logger: this.logger,
      });

      // 4. (Optional) Update database record for the new compressed file
      // This depends on your data model. You might want to create a new record
      // or update the existing one with info about the compressed version.
      this.logger.info(
        { fileId: id, compressedFileName },
        "Image compressed successfully"
      );
    } catch (error) {
      throw new ServiceError(
        `Failed to handle compress image job for fileId: ${id}`,
        error as Error
      );
    }
  }
}
