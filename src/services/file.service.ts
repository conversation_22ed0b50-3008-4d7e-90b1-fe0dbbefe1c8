import type Stream from "node:stream";
import type { File } from "../database/schema";

export abstract class FileService {
  abstract getSize(fileName: string): Promise<number>;
  abstract delete(fileName: string): Promise<void>;
  abstract write(fileName: string, data: Buffer): Promise<void>;
  abstract createWriteStream(fileName: string): Stream.Writable;
  abstract createReadStream(fileName: string): Stream.Readable;
  abstract getFileInfo(id: number): Promise<File>;
  abstract uploadStream(params: {
    fileName: string;
    readStream: Stream.Readable;
    mimeType: string;
    onError?: (error: Error) => void;
    onFinish?: () => void;
  }): Promise<{ id: number }>;
}
