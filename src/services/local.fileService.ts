import fs from "node:fs";
import type { Readable, Writable } from "node:stream";
import { inject, singleton } from "tsyringe";
import type { FileService } from "./file.service";
import type { FileRepository } from "../repositories/file.repository";
import { appConfig } from "../config";
import { STORAGE_TYPE } from "../constants/file";
import { FileServiceError, NotFoundError } from "../types/errors";

@singleton()
export class LocalFileService implements FileService {
	folderPath: string;

	constructor(
		folderPath: string,
		@inject("FileRepository") private fileRepository: FileRepository,
	) {
		this.folderPath = `${appConfig.staticPath}/${folderPath}`;

		if (!fs.existsSync(this.folderPath)) {
			fs.mkdirSync(this.folderPath, { recursive: true });
		}
	}

	async delete(fileName: string): Promise<void> {
		const filePath = this.getFilePath(fileName);

		try {
			await fs.promises.unlink(filePath);
		} catch (error) {
			throw new FileServiceError(
				`Failed to delete file, path ${filePath}`,
				error as Error,
			);
		}
	}

	async getSize(fileName: string): Promise<number> {
		const filePath = this.getFilePath(fileName);

		try {
			const stats = await fs.promises.stat(filePath);
			return stats.size;
		} catch (error) {
			throw new FileServiceError(
				`Failed to get file size, path ${filePath}`,
				error as Error,
			);
		}
	}

	async write(fileName: string, data: Buffer) {
		const filePath = this.getFilePath(fileName);

		try {
			await fs.promises.writeFile(filePath, data);
		} catch (error) {
			throw new FileServiceError(
				`Failed to write file, path ${filePath}`,
				error as Error,
			);
		}
	}

	createWriteStream(fileName: string): Writable {
		const filePath = this.getFilePath(fileName);

		return fs.createWriteStream(filePath);
	}

	createReadStream(fileName: string): Readable {
		const filePath = this.getFilePath(fileName);

		return fs.createReadStream(filePath);
	}

	getFilePath(fileName: string) {
		return `${this.folderPath}/${fileName}`;
	}

	async uploadStream(args: {
		fileName: string;
		mimeType: string;
		readStream: Readable;
		options?: {
			onError?: (error: Error) => void;
			onFinish?: () => void;
		};
	}): Promise<{ id: number }> {
		const { fileName, mimeType, readStream, options } = args;

		try {
			const writeStream = this.createWriteStream(fileName);

			await new Promise((resolve, reject) => {
				readStream.pipe(writeStream);

				readStream.on("error", async (error) => {
					await this.delete(fileName);
					options?.onError?.(error);
					reject(error);
				});

				writeStream.on("error", async (error) => {
					await this.delete(fileName);
					options?.onError?.(error);
					reject(error);
				});

				writeStream.on("finish", () => {
					options?.onFinish?.();
					resolve(undefined);
				});
			});

			const fileId = await this.fileRepository.create({
				name: fileName,
				mimeType,
				storagePath: this.folderPath,
				uploadTimestamp: new Date(),
				storageType: STORAGE_TYPE.LOCAL,
			});

			return { id: fileId };
		} catch (error) {
			await this.delete(fileName);
			throw new FileServiceError(
				`Failed to write file, path ${this.folderPath}`,
				error as Error,
			);
		}
	}

	async getFileInfo(id: number) {
		const file = await this.fileRepository.findById(id);

		if (!file) {
			throw new NotFoundError(`File with id ${id} not found`);
		}

		return file;
	}
}
