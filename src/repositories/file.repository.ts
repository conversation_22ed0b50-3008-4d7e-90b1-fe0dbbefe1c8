import { singleton } from "tsyringe";
import { eq, inArray } from "drizzle-orm";
import { filesTable, type File, type InsertFile } from "../database/schema";
import db from "../database/setup";
import logger from "../utils/logger";

@singleton()
export class FileRepository {
  async create(data: InsertFile): Promise<number> {
    const inserted = await db
      .insert(filesTable)
      .values(data)
      .returning({ insertedId: filesTable.id });

    if (!inserted.at(0)) {
      logger.child({ data }).error("Failed to insert image");

      throw Error("Failed to insert image");
    }

    return inserted[0].insertedId;
  }

  async findById(id: number): Promise<File | null> {
    const image = await db
      .select()
      .from(filesTable)
      .where(eq(filesTable.id, id))
      .limit(1);

    if (!image.at(0)) {
      logger.child({ id }).warn("Failed to find image");
    }

    return image.at(0) ?? null;
  }

  async findByIds(ids: number[]): Promise<File[]> {
    const images = await db
      .select()
      .from(filesTable)
      .where(inArray(filesTable.id, ids));

    return images;
  }

  async update(id: number, data: Partial<File>): Promise<void> {
    await db.update(filesTable).set(data).where(eq(filesTable.id, id));
  }

  async existById(id: number): Promise<boolean> {
    const image = await db
      .select()
      .from(filesTable)
      .where(eq(filesTable.id, id))
      .limit(1);

    return image.length > 0;
  }
}
