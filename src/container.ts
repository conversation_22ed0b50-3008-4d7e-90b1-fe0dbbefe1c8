import { container } from "tsyringe";
import { FileRepository } from "./repositories/file.repository";
import { LocalFileService } from "./services/local.fileService";
import { ImageService } from "./services/image.service";

// Register the repository
container.register("FileRepository", {
	useClass: FileRepository,
});

// Register the file service
// We can switch between LocalFileService and other services (e.g., S3) here
container.register("FileService", {
	useFactory: (c) =>
		new LocalFileService("uploads", c.resolve("FileRepository")),
});

// Register the image service
container.register("ImageService", {
	useClass: ImageService,
});

export default container;
