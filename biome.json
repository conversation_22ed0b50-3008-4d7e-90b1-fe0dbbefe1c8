{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "tab"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off"}, "style": {"noNonNullAssertion": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}, "parser": {"unsafeParameterDecoratorsEnabled": true}}}